@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/tokens/m3';

// The prefix used to generate the fully qualified name for tokens in this file.
$prefix: (mat, tree);

/// Generates custom tokens for the mat-tree.
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  @return (
    base: (),
    color: (
      tree-container-background-color: map.get($system, surface),
      tree-node-text-color: map.get($system, on-surface),
    ),
    typography: (
      tree-node-text-font: map.get($system, body-large-font),
      tree-node-text-size: map.get($system, body-large-size),
      tree-node-text-weight: map.get($system, body-large-weight),
    ),
    density: get-density-tokens(map.get($system, density-scale)),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -4);
  $index: ($scale * -1) + 1;

  @return (
    tree-node-min-height: list.nth((48px, 44px, 40px, 36px, 28px), $index),
  );
}
