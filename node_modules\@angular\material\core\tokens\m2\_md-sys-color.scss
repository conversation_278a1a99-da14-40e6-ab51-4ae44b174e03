//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';
@use '../../m2/palette';

@function md-sys-color-values-dark($palettes) {
  @return (
    primary: map.get($palettes, primary, default),
    on-primary: map.get($palettes, primary, default-contrast),
    inverse-primary: map.get($palettes, primary, 600),
    secondary: map.get($palettes, accent, default),
    on-secondary: map.get($palettes, accent, default-contrast),
    inverse-secondary: map.get($palettes, accent, 600),
    error: map.get($palettes, warn, default),
    on-error: map.get($palettes, warn, default-contrast),
    inverse-error: map.get($palettes, warn, 600),
    shadow: black,
    surface: map.get(palette.$grey-palette, 800),
    on-surface: white,
    surface-variant: #4a4a4a,
    on-surface-variant: rgba(white, 0.7),
    background: #303030,
    inverse-surface: white,
    inverse-on-surface: rgba(black, 0.87),
    outline: rgba(white, 0.12),
    outline-variant: rgba(white, 0.38),

    // TBD
    error-container: null,
    on-background: null,
    on-error-container: null,
    on-primary-container: null,
    on-primary-fixed: null,
    on-primary-fixed-variant: null,
    on-secondary-container: null,
    on-secondary-fixed: null,
    on-secondary-fixed-variant: null,
    on-tertiary: null,
    on-tertiary-container: null,
    on-tertiary-fixed: null,
    on-tertiary-fixed-variant: null,
    primary-container: null,
    primary-fixed: null,
    primary-fixed-dim: null,
    scrim: null,
    secondary-container: null,
    secondary-fixed: null,
    secondary-fixed-dim: null,
    surface-bright: null,
    surface-container: null,
    surface-container-high: null,
    surface-container-highest: null,
    surface-container-low: null,
    surface-container-lowest: null,
    surface-dim: null,
    surface-tint: null,
    tertiary: null,
    tertiary-container: null,
    tertiary-fixed: null,
    tertiary-fixed-dim: null,
  );
}

@function md-sys-color-values-light($palettes) {
  @return (
    primary: map.get($palettes, primary, default),
    on-primary: map.get($palettes, primary, default-contrast),
    inverse-primary: map.get($palettes, primary, 300),
    secondary: map.get($palettes, accent, default),
    on-secondary: map.get($palettes, accent, default-contrast),
    inverse-secondary: map.get($palettes, accent, 300),
    error: map.get($palettes, warn, default),
    on-error: map.get($palettes, warn, default-contrast),
    inverse-error: map.get($palettes, warn, 300),
    shadow: black,
    surface: white,
    on-surface: rgba(black, 0.87),
    surface-variant: #f6f6f6,
    on-surface-variant: rgba(black, 0.54),
    background: map.get(palette.$grey-palette, 50),
    inverse-surface: map.get(palette.$grey-palette, 800),
    inverse-on-surface: white,
    outline: rgba(black, 0.12),
    outline-variant: rgba(black, 0.38),

    // TBD
    error-container: null,
    on-background: null,
    on-error-container: null,
    on-primary-container: null,
    on-primary-fixed: null,
    on-primary-fixed-variant: null,
    on-secondary-container: null,
    on-secondary-fixed: null,
    on-secondary-fixed-variant: null,
    on-tertiary: null,
    on-tertiary-container: null,
    on-tertiary-fixed: null,
    on-tertiary-fixed-variant: null,
    primary-container: null,
    primary-fixed: null,
    primary-fixed-dim: null,
    scrim: null,
    secondary-container: null,
    secondary-fixed: null,
    secondary-fixed-dim: null,
    surface-bright: null,
    surface-container: null,
    surface-container-high: null,
    surface-container-highest: null,
    surface-container-low: null,
    surface-container-lowest: null,
    surface-dim: null,
    surface-tint: null,
    tertiary: null,
    tertiary-container: null,
    tertiary-fixed: null,
    tertiary-fixed-dim: null,
  );
}
