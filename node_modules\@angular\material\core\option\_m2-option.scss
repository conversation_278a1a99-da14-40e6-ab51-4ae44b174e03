@use 'sass:map';
@use '../tokens/m2-utils';
@use '../tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (),
    color: private-get-color-palette-color-tokens($theme, primary),
    typography: (
      option-label-text-font: map.get($system, body-large-font),
      option-label-text-line-height: map.get($system, body-large-line-height),
      option-label-text-size: map.get($system, body-large-size),
      option-label-text-tracking: map.get($system, body-large-tracking),
      option-label-text-weight: map.get($system, body-large-weight)
    ),
    density: (),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);

  @return (
    option-selected-state-label-text-color: map.get($system, primary),
    option-label-text-color: map.get($system, on-surface),
    option-hover-state-layer-color: m3-utils.color-with-opacity(
        map.get($system, on-surface), map.get($system, hover-state-layer-opacity)),
    option-focus-state-layer-color: m3-utils.color-with-opacity(
        map.get($system, on-surface), map.get($system, focus-state-layer-opacity)),
    option-selected-state-layer-color: m3-utils.color-with-opacity(
        map.get($system, on-surface), map.get($system, pressed-state-layer-opacity)),
  );
}

