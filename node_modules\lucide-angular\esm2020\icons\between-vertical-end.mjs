/**
 * @component @name BetweenVerticalEnd
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeD0iMyIgeT0iMyIgcng9IjEiIC8+CiAgPHBhdGggZD0ibTkgMjIgMy0zIDMgMyIgLz4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeD0iMTQiIHk9IjMiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/between-vertical-end
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BetweenVerticalEnd = [
    ['rect', { width: '7', height: '13', x: '3', y: '3', rx: '1', key: '1fdu0f' }],
    ['path', { d: 'm9 22 3-3 3 3', key: '17z65a' }],
    ['rect', { width: '7', height: '13', x: '14', y: '3', rx: '1', key: '1squn4' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BetweenVerticalEnd;
//# sourceMappingURL=data:application/json;base64,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