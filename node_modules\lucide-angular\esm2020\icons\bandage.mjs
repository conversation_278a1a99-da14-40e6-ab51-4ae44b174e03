/**
 * @component @name Bandage
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTAuMDFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCAxNC4wMWguMDEiIC8+CiAgPHBhdGggZD0iTTE0IDEwLjAxaC4wMSIgLz4KICA8cGF0aCBkPSJNMTQgMTQuMDFoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xOCA2djExLjUiIC8+CiAgPHBhdGggZD0iTTYgNnYxMiIgLz4KICA8cmVjdCB4PSIyIiB5PSI2IiB3aWR0aD0iMjAiIGhlaWdodD0iMTIiIHJ4PSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/bandage
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Bandage = [
    ['path', { d: 'M10 10.01h.01', key: '1e9xi7' }],
    ['path', { d: 'M10 14.01h.01', key: 'ac23bv' }],
    ['path', { d: 'M14 10.01h.01', key: '2wfrvf' }],
    ['path', { d: 'M14 14.01h.01', key: '8tw8yn' }],
    ['path', { d: 'M18 6v11.5', key: 'dkbidh' }],
    ['path', { d: 'M6 6v12', key: 'vkc79e' }],
    ['rect', { x: '2', y: '6', width: '20', height: '12', rx: '2', key: '1wpnh2' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Bandage;
//# sourceMappingURL=data:application/json;base64,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