/**
 * @component @name Barrel
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgM2E0MSA0MSAwIDAgMCAwIDE4IiAvPgogIDxwYXRoIGQ9Ik0xNCAzYTQxIDQxIDAgMCAxIDAgMTgiIC8+CiAgPHBhdGggZD0iTTE3IDNhMiAyIDAgMCAxIDEuNjguOTIgMTUuMjUgMTUuMjUgMCAwIDEgMCAxNi4xNkEyIDIgMCAwIDEgMTcgMjFIN2EyIDIgMCAwIDEtMS42OC0uOTIgMTUuMjUgMTUuMjUgMCAwIDEgMC0xNi4xNkEyIDIgMCAwIDEgNyAzeiIgLz4KICA8cGF0aCBkPSJNMy44NCAxN2gxNi4zMiIgLz4KICA8cGF0aCBkPSJNMy44NCA3aDE2LjMyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/barrel
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Barrel = [
    ['path', { d: 'M10 3a41 41 0 0 0 0 18', key: '1qcnzb' }],
    ['path', { d: 'M14 3a41 41 0 0 1 0 18', key: '547vd4' }],
    [
        'path',
        {
            d: 'M17 3a2 2 0 0 1 1.68.92 15.25 15.25 0 0 1 0 16.16A2 2 0 0 1 17 21H7a2 2 0 0 1-1.68-.92 15.25 15.25 0 0 1 0-16.16A2 2 0 0 1 7 3z',
            key: '1wepyy',
        },
    ],
    ['path', { d: 'M3.84 17h16.32', key: '1wh981' }],
    ['path', { d: 'M3.84 7h16.32', key: '19jf4x' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Barrel;
//# sourceMappingURL=data:application/json;base64,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