/**
 * @component @name BeanOff
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOSA5Yy0uNjQuNjQtMS41MjEuOTU0LTIuNDAyIDEuMTY1QTYgNiAwIDAgMCA4IDIyYTEzLjk2IDEzLjk2IDAgMCAwIDkuOS00LjEiIC8+CiAgPHBhdGggZD0iTTEwLjc1IDUuMDkzQTYgNiAwIDAgMSAyMiA4YzAgMi40MTEtLjYxIDQuNjgtMS42ODMgNi42NiIgLz4KICA8cGF0aCBkPSJNNS4zNDEgMTAuNjJhNCA0IDAgMCAwIDYuNDg3IDEuMjA4TTEwLjYyIDUuMzQxYTQuMDE1IDQuMDE1IDAgMCAxIDIuMDM5IDIuMDQiIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSIyMiIgeTE9IjIiIHkyPSIyMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bean-off
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BeanOff = [
    [
        'path',
        {
            d: 'M9 9c-.64.64-1.521.954-2.402 1.165A6 6 0 0 0 8 22a13.96 13.96 0 0 0 9.9-4.1',
            key: 'bq3udt',
        },
    ],
    ['path', { d: 'M10.75 5.093A6 6 0 0 1 22 8c0 2.411-.61 4.68-1.683 6.66', key: '17ccse' }],
    [
        'path',
        {
            d: 'M5.341 10.62a4 4 0 0 0 6.487 1.208M10.62 5.341a4.015 4.015 0 0 1 2.039 2.04',
            key: '18zqgq',
        },
    ],
    ['line', { x1: '2', x2: '22', y1: '2', y2: '22', key: 'a6p6uj' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BeanOff;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmVhbi1vZmYuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi9zcmMvaWNvbnMvYmVhbi1vZmYudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBRUE7Ozs7Ozs7Ozs7R0FVRztBQUNILE1BQU0sT0FBTyxHQUFtQjtJQUM5QjtRQUNFLE1BQU07UUFDTjtZQUNFLENBQUMsRUFBRSw2RUFBNkU7WUFDaEYsR0FBRyxFQUFFLFFBQVE7U0FDZDtLQUNGO0lBQ0QsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUseURBQXlELEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQ3pGO1FBQ0UsTUFBTTtRQUNOO1lBQ0UsQ0FBQyxFQUFFLDZFQUE2RTtZQUNoRixHQUFHLEVBQUUsUUFBUTtTQUNkO0tBQ0Y7SUFDRCxDQUFDLE1BQU0sRUFBRSxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsRUFBRSxFQUFFLElBQUksRUFBRSxFQUFFLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRSxJQUFJLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0NBQ2xFLENBQUMsQ0FBQyxnREFBZ0Q7QUFFbkQsZUFBZSxPQUFPLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMdWNpZGVJY29uRGF0YSB9IGZyb20gJy4vdHlwZXMnO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQmVhbk9mZlxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOT1NBNVl5MHVOalF1TmpRdE1TNDFNakV1T1RVMExUSXVOREF5SURFdU1UWTFRVFlnTmlBd0lEQWdNQ0E0SURJeVlURXpMamsySURFekxqazJJREFnTUNBd0lEa3VPUzAwTGpFaUlDOCtDaUFnUEhCaGRHZ2daRDBpVFRFd0xqYzFJRFV1TURrelFUWWdOaUF3SURBZ01TQXlNaUE0WXpBZ01pNDBNVEV0TGpZeElEUXVOamd0TVM0Mk9ETWdOaTQyTmlJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTlM0ek5ERWdNVEF1TmpKaE5DQTBJREFnTUNBd0lEWXVORGczSURFdU1qQTRUVEV3TGpZeUlEVXVNelF4WVRRdU1ERTFJRFF1TURFMUlEQWdNQ0F4SURJdU1ETTVJREl1TURRaUlDOCtDaUFnUEd4cGJtVWdlREU5SWpJaUlIZ3lQU0l5TWlJZ2VURTlJaklpSUhreVBTSXlNaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvYmVhbi1vZmZcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS12dWUtbmV4dCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7RnVuY3Rpb25hbENvbXBvbmVudH0gVnVlIGNvbXBvbmVudFxuICpcbiAqL1xuY29uc3QgQmVhbk9mZjogTHVjaWRlSWNvbkRhdGEgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ005IDljLS42NC42NC0xLjUyMS45NTQtMi40MDIgMS4xNjVBNiA2IDAgMCAwIDggMjJhMTMuOTYgMTMuOTYgMCAwIDAgOS45LTQuMScsXG4gICAgICBrZXk6ICdicTN1ZHQnLFxuICAgIH0sXG4gIF0sXG4gIFsncGF0aCcsIHsgZDogJ00xMC43NSA1LjA5M0E2IDYgMCAwIDEgMjIgOGMwIDIuNDExLS42MSA0LjY4LTEuNjgzIDYuNjYnLCBrZXk6ICcxN2Njc2UnIH1dLFxuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNNS4zNDEgMTAuNjJhNCA0IDAgMCAwIDYuNDg3IDEuMjA4TTEwLjYyIDUuMzQxYTQuMDE1IDQuMDE1IDAgMCAxIDIuMDM5IDIuMDQnLFxuICAgICAga2V5OiAnMTh6cWdxJyxcbiAgICB9LFxuICBdLFxuICBbJ2xpbmUnLCB7IHgxOiAnMicsIHgyOiAnMjInLCB5MTogJzInLCB5MjogJzIyJywga2V5OiAnYTZwNnVqJyB9XSxcbl07IC8vZXNsaW50LWRpc2FibGUtbGluZSBuby1zaGFkb3ctcmVzdHJpY3RlZC1uYW1lc1xuXG5leHBvcnQgZGVmYXVsdCBCZWFuT2ZmO1xuIl19