/**
 * @component @name BadgeJapaneseYen
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMy44NSA4LjYyYTQgNCAwIDAgMSA0Ljc4LTQuNzcgNCA0IDAgMCAxIDYuNzQgMCA0IDQgMCAwIDEgNC43OCA0Ljc4IDQgNCAwIDAgMSAwIDYuNzQgNCA0IDAgMCAxLTQuNzcgNC43OCA0IDQgMCAwIDEtNi43NSAwIDQgNCAwIDAgMS00Ljc4LTQuNzcgNCA0IDAgMCAxIDAtNi43NloiIC8+CiAgPHBhdGggZD0ibTkgOCAzIDN2NyIgLz4KICA8cGF0aCBkPSJtMTIgMTEgMy0zIiAvPgogIDxwYXRoIGQ9Ik05IDEyaDYiIC8+CiAgPHBhdGggZD0iTTkgMTZoNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/badge-japanese-yen
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BadgeJapaneseYen = [
    [
        'path',
        {
            d: 'M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z',
            key: '3c2336',
        },
    ],
    ['path', { d: 'm9 8 3 3v7', key: '17yadx' }],
    ['path', { d: 'm12 11 3-3', key: 'p4cfq1' }],
    ['path', { d: 'M9 12h6', key: '1c52cq' }],
    ['path', { d: 'M9 16h6', key: '8wimt3' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BadgeJapaneseYen;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmFkZ2UtamFwYW5lc2UteWVuLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL2ljb25zL2JhZGdlLWphcGFuZXNlLXllbi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQTs7Ozs7Ozs7OztHQVVHO0FBQ0gsTUFBTSxnQkFBZ0IsR0FBbUI7SUFDdkM7UUFDRSxNQUFNO1FBQ047WUFDRSxDQUFDLEVBQUUsaUtBQWlLO1lBQ3BLLEdBQUcsRUFBRSxRQUFRO1NBQ2Q7S0FDRjtJQUNELENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxFQUFFLFlBQVksRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUM7SUFDNUMsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUsWUFBWSxFQUFFLEdBQUcsRUFBRSxRQUFRLEVBQUUsQ0FBQztJQUM1QyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQ3pDLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxFQUFFLFNBQVMsRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUM7Q0FDMUMsQ0FBQyxDQUFDLGdEQUFnRDtBQUVuRCxlQUFlLGdCQUFnQixDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTHVjaWRlSWNvbkRhdGEgfSBmcm9tICcuL3R5cGVzJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIEJhZGdlSmFwYW5lc2VZZW5cbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk15NDROU0E0TGpZeVlUUWdOQ0F3SURBZ01TQTBMamM0TFRRdU56Y2dOQ0EwSURBZ01DQXhJRFl1TnpRZ01DQTBJRFFnTUNBd0lERWdOQzQzT0NBMExqYzRJRFFnTkNBd0lEQWdNU0F3SURZdU56UWdOQ0EwSURBZ01DQXhMVFF1TnpjZ05DNDNPQ0EwSURRZ01DQXdJREV0Tmk0M05TQXdJRFFnTkNBd0lEQWdNUzAwTGpjNExUUXVOemNnTkNBMElEQWdNQ0F4SURBdE5pNDNObG9pSUM4K0NpQWdQSEJoZEdnZ1pEMGliVGtnT0NBeklETjJOeUlnTHo0S0lDQThjR0YwYUNCa1BTSnRNVElnTVRFZ015MHpJaUF2UGdvZ0lEeHdZWFJvSUdROUlrMDVJREV5YURZaUlDOCtDaUFnUEhCaGRHZ2daRDBpVFRrZ01UWm9OaUlnTHo0S1BDOXpkbWMrQ2c9PSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvYmFkZ2UtamFwYW5lc2UteWVuXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtdnVlLW5leHQgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0Z1bmN0aW9uYWxDb21wb25lbnR9IFZ1ZSBjb21wb25lbnRcbiAqXG4gKi9cbmNvbnN0IEJhZGdlSmFwYW5lc2VZZW46IEx1Y2lkZUljb25EYXRhID0gW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNMy44NSA4LjYyYTQgNCAwIDAgMSA0Ljc4LTQuNzcgNCA0IDAgMCAxIDYuNzQgMCA0IDQgMCAwIDEgNC43OCA0Ljc4IDQgNCAwIDAgMSAwIDYuNzQgNCA0IDAgMCAxLTQuNzcgNC43OCA0IDQgMCAwIDEtNi43NSAwIDQgNCAwIDAgMS00Ljc4LTQuNzcgNCA0IDAgMCAxIDAtNi43NlonLFxuICAgICAga2V5OiAnM2MyMzM2JyxcbiAgICB9LFxuICBdLFxuICBbJ3BhdGgnLCB7IGQ6ICdtOSA4IDMgM3Y3Jywga2V5OiAnMTd5YWR4JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTEyIDExIDMtMycsIGtleTogJ3A0Y2ZxMScgfV0sXG4gIFsncGF0aCcsIHsgZDogJ005IDEyaDYnLCBrZXk6ICcxYzUyY3EnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNOSAxNmg2Jywga2V5OiAnOHdpbXQzJyB9XSxcbl07IC8vZXNsaW50LWRpc2FibGUtbGluZSBuby1zaGFkb3ctcmVzdHJpY3RlZC1uYW1lc1xuXG5leHBvcnQgZGVmYXVsdCBCYWRnZUphcGFuZXNlWWVuO1xuIl19