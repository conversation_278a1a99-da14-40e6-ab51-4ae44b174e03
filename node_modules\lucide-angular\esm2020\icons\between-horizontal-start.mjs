/**
 * @component @name BetweenHorizontalStart
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTMiIGhlaWdodD0iNyIgeD0iOCIgeT0iMyIgcng9IjEiIC8+CiAgPHBhdGggZD0ibTIgOSAzIDMtMyAzIiAvPgogIDxyZWN0IHdpZHRoPSIxMyIgaGVpZ2h0PSI3IiB4PSI4IiB5PSIxNCIgcng9IjEiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/between-horizontal-start
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BetweenHorizontalStart = [
    ['rect', { width: '13', height: '7', x: '8', y: '3', rx: '1', key: 'pkso9a' }],
    ['path', { d: 'm2 9 3 3-3 3', key: '1agib5' }],
    ['rect', { width: '13', height: '7', x: '8', y: '14', rx: '1', key: '1q5fc1' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BetweenHorizontalStart;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmV0d2Vlbi1ob3Jpem9udGFsLXN0YXJ0LmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vc3JjL2ljb25zL2JldHdlZW4taG9yaXpvbnRhbC1zdGFydC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQTs7Ozs7Ozs7OztHQVVHO0FBQ0gsTUFBTSxzQkFBc0IsR0FBbUI7SUFDN0MsQ0FBQyxNQUFNLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsR0FBRyxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQzlFLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxFQUFFLGNBQWMsRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUM7SUFDOUMsQ0FBQyxNQUFNLEVBQUUsRUFBRSxLQUFLLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxHQUFHLEVBQUUsQ0FBQyxFQUFFLEdBQUcsRUFBRSxDQUFDLEVBQUUsSUFBSSxFQUFFLEVBQUUsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0NBQ2hGLENBQUMsQ0FBQyxnREFBZ0Q7QUFFbkQsZUFBZSxzQkFBc0IsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEx1Y2lkZUljb25EYXRhIH0gZnJvbSAnLi90eXBlcyc7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBCZXR3ZWVuSG9yaXpvbnRhbFN0YXJ0XG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjbVZqZENCM2FXUjBhRDBpTVRNaUlHaGxhV2RvZEQwaU55SWdlRDBpT0NJZ2VUMGlNeUlnY25nOUlqRWlJQzgrQ2lBZ1BIQmhkR2dnWkQwaWJUSWdPU0F6SURNdE15QXpJaUF2UGdvZ0lEeHlaV04wSUhkcFpIUm9QU0l4TXlJZ2FHVnBaMmgwUFNJM0lpQjRQU0k0SWlCNVBTSXhOQ0lnY25nOUlqRWlJQzgrQ2p3dmMzWm5QZ289KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9iZXR3ZWVuLWhvcml6b250YWwtc3RhcnRcbiAqIEBzZWUgaHR0cHM6Ly9sdWNpZGUuZGV2L2d1aWRlL3BhY2thZ2VzL2x1Y2lkZS12dWUtbmV4dCAtIERvY3VtZW50YXRpb25cbiAqXG4gKiBAcGFyYW0ge09iamVjdH0gcHJvcHMgLSBMdWNpZGUgaWNvbnMgcHJvcHMgYW5kIGFueSB2YWxpZCBTVkcgYXR0cmlidXRlXG4gKiBAcmV0dXJucyB7RnVuY3Rpb25hbENvbXBvbmVudH0gVnVlIGNvbXBvbmVudFxuICpcbiAqL1xuY29uc3QgQmV0d2Vlbkhvcml6b250YWxTdGFydDogTHVjaWRlSWNvbkRhdGEgPSBbXG4gIFsncmVjdCcsIHsgd2lkdGg6ICcxMycsIGhlaWdodDogJzcnLCB4OiAnOCcsIHk6ICczJywgcng6ICcxJywga2V5OiAncGtzbzlhJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnbTIgOSAzIDMtMyAzJywga2V5OiAnMWFnaWI1JyB9XSxcbiAgWydyZWN0JywgeyB3aWR0aDogJzEzJywgaGVpZ2h0OiAnNycsIHg6ICc4JywgeTogJzE0Jywgcng6ICcxJywga2V5OiAnMXE1ZmMxJyB9XSxcbl07IC8vZXNsaW50LWRpc2FibGUtbGluZSBuby1zaGFkb3ctcmVzdHJpY3RlZC1uYW1lc1xuXG5leHBvcnQgZGVmYXVsdCBCZXR3ZWVuSG9yaXpvbnRhbFN0YXJ0O1xuIl19