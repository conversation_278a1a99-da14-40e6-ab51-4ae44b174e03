@use 'sass:map';
@use 'sass:meta';
@use './m2';

// Gets the theme's system values as a flat map.
@function get-system($theme) {
  @if meta.type-of($theme) == map {
    $system: map.get($theme, _mat-system);
    @if $system {
      @return $system;
    }

    // If the $theme is a typography-config, convert it to a system map.
    @if map.has-key($theme, body-1) {
      @return m2.md-sys-typescale-values($theme);
    }
  }

  // If the $theme is a density scale, convert it to a system map.
  @if meta.type-of($theme) == 'number' or $theme == minimum or $theme == maximum {
    @return (density-scale: $theme);
  }

  @return ();
}
