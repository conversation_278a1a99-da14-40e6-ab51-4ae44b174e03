/**
 * @component @name RotateCwSquare
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNUg2YTIgMiAwIDAgMC0yIDJ2MyIgLz4KICA8cGF0aCBkPSJtOSA4IDMtMy0zLTMiIC8+CiAgPHBhdGggZD0iTTQgMTR2NGEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWN2EyIDIgMCAwIDAtMi0yaC0yIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-cw-square
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const RotateCwSquare = [
    ['path', { d: 'M12 5H6a2 2 0 0 0-2 2v3', key: 'l96uqu' }],
    ['path', { d: 'm9 8 3-3-3-3', key: '1gzgc3' }],
    ['path', { d: 'M4 14v4a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2h-2', key: '1w2k5h' }],
]; //eslint-disable-line no-shadow-restricted-names
export default RotateCwSquare;
//# sourceMappingURL=data:application/json;base64,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