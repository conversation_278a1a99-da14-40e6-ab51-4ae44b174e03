/**
 * @component @name Package2
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3Y2IiAvPgogIDxwYXRoIGQ9Ik0xNi43NiAzYTIgMiAwIDAgMSAxLjggMS4xbDIuMjMgNC40NzlhMiAyIDAgMCAxIC4yMS44OTFWMTlhMiAyIDAgMCAxLTIgMkg1YTIgMiAwIDAgMS0yLTJWOS40NzJhMiAyIDAgMCAxIC4yMTEtLjg5NEw1LjQ1IDQuMUEyIDIgMCAwIDEgNy4yNCAzeiIgLz4KICA8cGF0aCBkPSJNMy4wNTQgOS4wMTNoMTcuODkzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/package-2
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Package2 = [
    ['path', { d: 'M12 3v6', key: '1holv5' }],
    [
        'path',
        {
            d: 'M16.76 3a2 2 0 0 1 1.8 1.1l2.23 4.479a2 2 0 0 1 .21.891V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9.472a2 2 0 0 1 .211-.894L5.45 4.1A2 2 0 0 1 7.24 3z',
            key: '187q7i',
        },
    ],
    ['path', { d: 'M3.054 9.013h17.893', key: 'grwhos' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Package2;
//# sourceMappingURL=data:application/json;base64,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