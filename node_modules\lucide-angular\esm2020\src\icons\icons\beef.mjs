/**
 * @component @name Beef
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYuNCAxMy43QTYuNSA2LjUgMCAxIDAgNi4yOCA2LjZjLTEuMSAzLjEzLS43OCAzLjktMy4xOCA2LjA4QTMgMyAwIDAgMCA1IDE4YzQgMCA4LjQtMS44IDExLjQtNC4zIiAvPgogIDxwYXRoIGQ9Im0xOC41IDYgMi4xOSA0LjVhNi40OCA2LjQ4IDAgMCAxLTIuMjkgNy4yQzE1LjQgMjAuMiAxMSAyMiA3IDIyYTMgMyAwIDAgMS0yLjY4LTEuNjZMMi40IDE2LjUiIC8+CiAgPGNpcmNsZSBjeD0iMTIuNSIgY3k9IjguNSIgcj0iMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/beef
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Beef = [
    [
        'path',
        {
            d: 'M16.4 13.7A6.5 6.5 0 1 0 6.28 6.6c-1.1 3.13-.78 3.9-3.18 6.08A3 3 0 0 0 5 18c4 0 8.4-1.8 11.4-4.3',
            key: 'cisjcv',
        },
    ],
    [
        'path',
        {
            d: 'm18.5 6 2.19 4.5a6.48 6.48 0 0 1-2.29 7.2C15.4 20.2 11 22 7 22a3 3 0 0 1-2.68-1.66L2.4 16.5',
            key: '5byaag',
        },
    ],
    ['circle', { cx: '12.5', cy: '8.5', r: '2.5', key: '9738u8' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Beef;
//# sourceMappingURL=data:application/json;base64,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