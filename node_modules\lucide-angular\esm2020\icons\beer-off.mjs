/**
 * @component @name BeerOff
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTMgMTN2NSIgLz4KICA8cGF0aCBkPSJNMTcgMTEuNDdWOCIgLz4KICA8cGF0aCBkPSJNMTcgMTFoMWEzIDMgMCAwIDEgMi43NDUgNC4yMTEiIC8+CiAgPHBhdGggZD0ibTIgMiAyMCAyMCIgLz4KICA8cGF0aCBkPSJNNSA4djEyYTIgMiAwIDAgMCAyIDJoOGEyIDIgMCAwIDAgMi0ydi0zIiAvPgogIDxwYXRoIGQ9Ik03LjUzNiA3LjUzNUM2Ljc2NiA3LjY0OSA2LjE1NCA4IDUuNSA4YTIuNSAyLjUgMCAwIDEtMS43NjgtNC4yNjgiIC8+CiAgPHBhdGggZD0iTTguNzI3IDMuMjA0QzkuMzA2IDIuNzY3IDkuODg1IDIgMTEgMmMxLjU2IDAgMiAxLjUgMyAxLjVzMS43Mi0uNSAyLjUtLjVhMSAxIDAgMSAxIDAgNWMtLjc4IDAtMS41LS41LTIuNS0uNWEzLjE0OSAzLjE0OSAwIDAgMC0uODQyLjEyIiAvPgogIDxwYXRoIGQ9Ik05IDE0LjZWMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/beer-off
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BeerOff = [
    ['path', { d: 'M13 13v5', key: 'igwfh0' }],
    ['path', { d: 'M17 11.47V8', key: '16yw0g' }],
    ['path', { d: 'M17 11h1a3 3 0 0 1 2.745 4.211', key: '1xbt65' }],
    ['path', { d: 'm2 2 20 20', key: '1ooewy' }],
    ['path', { d: 'M5 8v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2v-3', key: 'c55o3e' }],
    [
        'path',
        { d: 'M7.536 7.535C6.766 7.649 6.154 8 5.5 8a2.5 2.5 0 0 1-1.768-4.268', key: '1ydug7' },
    ],
    [
        'path',
        {
            d: 'M8.727 3.204C9.306 2.767 9.885 2 11 2c1.56 0 2 1.5 3 1.5s1.72-.5 2.5-.5a1 1 0 1 1 0 5c-.78 0-1.5-.5-2.5-.5a3.149 3.149 0 0 0-.842.12',
            key: 'q81o7q',
        },
    ],
    ['path', { d: 'M9 14.6V18', key: '20ek98' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BeerOff;
//# sourceMappingURL=data:application/json;base64,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