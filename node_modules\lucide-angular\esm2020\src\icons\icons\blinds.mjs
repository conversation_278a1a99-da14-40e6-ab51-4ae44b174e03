/**
 * @component @name Blinds
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAzaDE4IiAvPgogIDxwYXRoIGQ9Ik0yMCA3SDgiIC8+CiAgPHBhdGggZD0iTTIwIDExSDgiIC8+CiAgPHBhdGggZD0iTTEwIDE5aDEwIiAvPgogIDxwYXRoIGQ9Ik04IDE1aDEyIiAvPgogIDxwYXRoIGQ9Ik00IDN2MTQiIC8+CiAgPGNpcmNsZSBjeD0iNCIgY3k9IjE5IiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/blinds
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Blinds = [
    ['path', { d: 'M3 3h18', key: 'o7r712' }],
    ['path', { d: 'M20 7H8', key: 'gd2fo2' }],
    ['path', { d: 'M20 11H8', key: '1ynp89' }],
    ['path', { d: 'M10 19h10', key: '19hjk5' }],
    ['path', { d: 'M8 15h12', key: '1yqzne' }],
    ['path', { d: 'M4 3v14', key: 'fggqzn' }],
    ['circle', { cx: '4', cy: '19', r: '2', key: 'p3m9r0' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Blinds;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYmxpbmRzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vc3JjL2ljb25zL2JsaW5kcy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFFQTs7Ozs7Ozs7OztHQVVHO0FBQ0gsTUFBTSxNQUFNLEdBQW1CO0lBQzdCLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxFQUFFLFNBQVMsRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUM7SUFDekMsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUsU0FBUyxFQUFFLEdBQUcsRUFBRSxRQUFRLEVBQUUsQ0FBQztJQUN6QyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBRSxVQUFVLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQzFDLENBQUMsTUFBTSxFQUFFLEVBQUUsQ0FBQyxFQUFFLFdBQVcsRUFBRSxHQUFHLEVBQUUsUUFBUSxFQUFFLENBQUM7SUFDM0MsQ0FBQyxNQUFNLEVBQUUsRUFBRSxDQUFDLEVBQUUsVUFBVSxFQUFFLEdBQUcsRUFBRSxRQUFRLEVBQUUsQ0FBQztJQUMxQyxDQUFDLE1BQU0sRUFBRSxFQUFFLENBQUMsRUFBRSxTQUFTLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0lBQ3pDLENBQUMsUUFBUSxFQUFFLEVBQUUsRUFBRSxFQUFFLEdBQUcsRUFBRSxFQUFFLEVBQUUsSUFBSSxFQUFFLENBQUMsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLFFBQVEsRUFBRSxDQUFDO0NBQ3pELENBQUMsQ0FBQyxnREFBZ0Q7QUFFbkQsZUFBZSxNQUFNLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBMdWNpZGVJY29uRGF0YSB9IGZyb20gJy4vdHlwZXMnO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgQmxpbmRzXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NeUF6YURFNElpQXZQZ29nSUR4d1lYUm9JR1E5SWsweU1DQTNTRGdpSUM4K0NpQWdQSEJoZEdnZ1pEMGlUVEl3SURFeFNEZ2lJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRURXdJREU1YURFd0lpQXZQZ29nSUR4d1lYUm9JR1E5SWswNElERTFhREV5SWlBdlBnb2dJRHh3WVhSb0lHUTlJazAwSUROMk1UUWlJQzgrQ2lBZ1BHTnBjbU5zWlNCamVEMGlOQ0lnWTNrOUlqRTVJaUJ5UFNJeUlpQXZQZ284TDNOMlp6NEspIC0gaHR0cHM6Ly9sdWNpZGUuZGV2L2ljb25zL2JsaW5kc1xuICogQHNlZSBodHRwczovL2x1Y2lkZS5kZXYvZ3VpZGUvcGFja2FnZXMvbHVjaWRlLXZ1ZS1uZXh0IC0gRG9jdW1lbnRhdGlvblxuICpcbiAqIEBwYXJhbSB7T2JqZWN0fSBwcm9wcyAtIEx1Y2lkZSBpY29ucyBwcm9wcyBhbmQgYW55IHZhbGlkIFNWRyBhdHRyaWJ1dGVcbiAqIEByZXR1cm5zIHtGdW5jdGlvbmFsQ29tcG9uZW50fSBWdWUgY29tcG9uZW50XG4gKlxuICovXG5jb25zdCBCbGluZHM6IEx1Y2lkZUljb25EYXRhID0gW1xuICBbJ3BhdGgnLCB7IGQ6ICdNMyAzaDE4Jywga2V5OiAnbzdyNzEyJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTIwIDdIOCcsIGtleTogJ2dkMmZvMicgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00yMCAxMUg4Jywga2V5OiAnMXlucDg5JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTEwIDE5aDEwJywga2V5OiAnMTloams1JyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTggMTVoMTInLCBrZXk6ICcxeXF6bmUnIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdNNCAzdjE0Jywga2V5OiAnZmdncXpuJyB9XSxcbiAgWydjaXJjbGUnLCB7IGN4OiAnNCcsIGN5OiAnMTknLCByOiAnMicsIGtleTogJ3AzbTlyMCcgfV0sXG5dOyAvL2VzbGludC1kaXNhYmxlLWxpbmUgbm8tc2hhZG93LXJlc3RyaWN0ZWQtbmFtZXNcblxuZXhwb3J0IGRlZmF1bHQgQmxpbmRzO1xuIl19