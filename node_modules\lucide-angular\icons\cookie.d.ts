import { LucideIconData } from './types';
/**
 * @component @name <PERSON>ie
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMmExMCAxMCAwIDEgMCAxMCAxMCA0IDQgMCAwIDEtNS01IDQgNCAwIDAgMS01LTUiIC8+CiAgPHBhdGggZD0iTTguNSA4LjV2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxNS41di4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTJ2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xMSAxN3YuMDEiIC8+CiAgPHBhdGggZD0iTTcgMTR2LjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/cookie
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
declare const Cookie: LucideIconData;
export default Cookie;
