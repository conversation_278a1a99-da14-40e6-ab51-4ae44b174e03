/**
 * @component @name Panda
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuMjUgMTcuMjVoMS41TDEyIDE4eiIgLz4KICA8cGF0aCBkPSJtMTUgMTIgMiAyIiAvPgogIDxwYXRoIGQ9Ik0xOCA2LjVhLjUuNSAwIDAgMC0uNS0uNSIgLz4KICA8cGF0aCBkPSJNMjAuNjkgOS42N2E0LjUgNC41IDAgMSAwLTcuMDQtNS41IDguMzUgOC4zNSAwIDAgMC0zLjMgMCA0LjUgNC41IDAgMSAwLTcuMDQgNS41QzIuNDkgMTEuMiAyIDEyLjg4IDIgMTQuNSAyIDE5LjQ3IDYuNDggMjIgMTIgMjJzMTAtMi41MyAxMC03LjVjMC0xLjYyLS40OC0zLjMtMS4zLTQuODMiIC8+CiAgPHBhdGggZD0iTTYgNi41YS40OTUuNDk1IDAgMCAxIC41LS41IiAvPgogIDxwYXRoIGQ9Im05IDEyLTIgMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/panda
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Panda = [
    ['path', { d: 'M11.25 17.25h1.5L12 18z', key: '1wmwwj' }],
    ['path', { d: 'm15 12 2 2', key: 'k60wz4' }],
    ['path', { d: 'M18 6.5a.5.5 0 0 0-.5-.5', key: '1ch4h4' }],
    [
        'path',
        {
            d: 'M20.69 9.67a4.5 4.5 0 1 0-7.04-5.5 8.35 8.35 0 0 0-3.3 0 4.5 4.5 0 1 0-7.04 5.5C2.49 11.2 2 12.88 2 14.5 2 19.47 6.48 22 12 22s10-2.53 10-7.5c0-1.62-.48-3.3-1.3-4.83',
            key: '1c660l',
        },
    ],
    ['path', { d: 'M6 6.5a.495.495 0 0 1 .5-.5', key: 'eviuep' }],
    ['path', { d: 'm9 12-2 2', key: '326nkw' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Panda;
//# sourceMappingURL=data:application/json;base64,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