/**
 * @component @name PackagePlus
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMTZoNiIgLz4KICA8cGF0aCBkPSJNMTkgMTN2NiIgLz4KICA8cGF0aCBkPSJNMjEgMTBWOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczbDcgNGEyIDIgMCAwIDAgMiAwbDItMS4xNCIgLz4KICA8cGF0aCBkPSJtNy41IDQuMjcgOSA1LjE1IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjkgNyAxMiAxMiAyMC43MSA3IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMjIiIHkyPSIxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/package-plus
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const PackagePlus = [
    ['path', { d: 'M16 16h6', key: '100bgy' }],
    ['path', { d: 'M19 13v6', key: '85cyf1' }],
    [
        'path',
        {
            d: 'M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14',
            key: 'e7tb2h',
        },
    ],
    ['path', { d: 'm7.5 4.27 9 5.15', key: '1c824w' }],
    ['polyline', { points: '3.29 7 12 12 20.71 7', key: 'ousv84' }],
    ['line', { x1: '12', x2: '12', y1: '22', y2: '12', key: 'a4e8g8' }],
]; //eslint-disable-line no-shadow-restricted-names
export default PackagePlus;
//# sourceMappingURL=data:application/json;base64,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