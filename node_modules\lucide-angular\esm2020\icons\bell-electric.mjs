/**
 * @component @name BellElectric
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTguNTE4IDE3LjM0N0E3IDcgMCAwIDEgMTQgMTkiIC8+CiAgPHBhdGggZD0iTTE4LjggNEExMSAxMSAwIDAgMSAyMCA5IiAvPgogIDxwYXRoIGQ9Ik05IDloLjAxIiAvPgogIDxjaXJjbGUgY3g9IjIwIiBjeT0iMTYiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjkiIHI9IjciIC8+CiAgPHJlY3QgeD0iNCIgeT0iMTYiIHdpZHRoPSIxMCIgaGVpZ2h0PSI2IiByeD0iMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bell-electric
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BellElectric = [
    ['path', { d: 'M18.518 17.347A7 7 0 0 1 14 19', key: '1emhpo' }],
    ['path', { d: 'M18.8 4A11 11 0 0 1 20 9', key: '127b67' }],
    ['path', { d: 'M9 9h.01', key: '1q5me6' }],
    ['circle', { cx: '20', cy: '16', r: '2', key: '1v9bxh' }],
    ['circle', { cx: '9', cy: '9', r: '7', key: 'p2h5vp' }],
    ['rect', { x: '4', y: '16', width: '10', height: '6', rx: '2', key: 'bfnviv' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BellElectric;
//# sourceMappingURL=data:application/json;base64,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