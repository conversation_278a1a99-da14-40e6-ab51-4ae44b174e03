import { LucideIconData } from './types';
/**
 * @component @name Cake
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMjF2LThhMiAyIDAgMCAwLTItMkg2YTIgMiAwIDAgMC0yIDJ2OCIgLz4KICA8cGF0aCBkPSJNNCAxNnMuNS0xIDItMSAyLjUgMiA0IDIgMi41LTIgNC0yIDIuNSAyIDQgMiAyLTEgMi0xIiAvPgogIDxwYXRoIGQ9Ik0yIDIxaDIwIiAvPgogIDxwYXRoIGQ9Ik03IDh2MyIgLz4KICA8cGF0aCBkPSJNMTIgOHYzIiAvPgogIDxwYXRoIGQ9Ik0xNyA4djMiIC8+CiAgPHBhdGggZD0iTTcgNGguMDEiIC8+CiAgPHBhdGggZD0iTTEyIDRoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNyA0aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/cake
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
declare const Cake: LucideIconData;
export default Cake;
