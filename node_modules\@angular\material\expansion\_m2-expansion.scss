@use 'sass:map';
@use '../core/theming/theming';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';
@use '../core/style/elevation';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -3);

  @return (
    base: (
      expansion-container-shape: 4px,
      expansion-container-elevation-shadow: elevation.get-box-shadow(2),
      expansion-legacy-header-indicator-display: inline-block,
      expansion-header-indicator-display: none,
    ),
    color: (
      expansion-container-background-color: map.get($system, surface),
      expansion-container-text-color: map.get($system, on-surface),
      expansion-actions-divider-color: map.get($system, outline),
      expansion-header-hover-state-layer-color: m3-utils.color-with-opacity(
          map.get($system, on-surface), map.get($system, hover-state-layer-opacity)),
      expansion-header-focus-state-layer-color: m3-utils.color-with-opacity(
          map.get($system, on-surface), map.get($system, focus-state-layer-opacity)),
      expansion-header-disabled-state-text-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      expansion-header-text-color: map.get($system, on-surface),
      expansion-header-description-color: map.get($system, on-surface-variant),
      expansion-header-indicator-color: map.get($system, on-surface-variant),
    ),
    typography: (
      expansion-header-text-font: map.get($system, label-medium-font),
      expansion-header-text-size: map.get($system, label-medium-size),
      expansion-header-text-weight: map.get($system, label-medium-weight),

      // TODO(crisbeto): these two properties weren't set at all before the introduction of tokens,
      // but it's inconsistent not to provide them since the container sets all of them. Eventually
      // we should change the values to use come from `subtitle-2`.
      expansion-header-text-line-height: inherit,
      expansion-header-text-tracking: inherit,

      expansion-container-text-font: map.get($system, body-medium-font),
      expansion-container-text-line-height: map.get($system, body-medium-line-height),
      expansion-container-text-size: map.get($system, body-medium-size),
      expansion-container-text-tracking: map.get($system, body-medium-tracking),
      expansion-container-text-weight: map.get($system, body-medium-weight),
    ),
    density: (
      expansion-header-collapsed-state-height: map.get((
        0: 48px,
        -1: 44px,
        -2: 40px,
        -3: 36px,
      ), $density-scale),
      expansion-header-expanded-state-height: map.get((
        0: 64px,
        -1: 60px,
        -2: 56px,
        -3: 48px,
      ), $density-scale),
    ),
  );
}
