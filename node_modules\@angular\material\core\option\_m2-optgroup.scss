@use '../tokens/m2-utils';
@use 'sass:map';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (),
    color: (
      optgroup-label-text-color: map.get($system, on-surface),
    ),
    typography: (
      optgroup-label-text-font: map.get($system, body-large-font),
      optgroup-label-text-line-height: map.get($system, body-large-line-height),
      optgroup-label-text-size: map.get($system, body-large-size),
      optgroup-label-text-tracking: map.get($system, body-large-tracking),
      optgroup-label-text-weight: map.get($system, body-large-weight)
    ),
    density: (),
  );
}
