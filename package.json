{"name": "qts-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:qts-angular": "node dist/qts-angular/server/server.mjs"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.1.2", "@angular/cdk": "^20.1.2", "@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/fire": "^20.0.1", "@angular/forms": "^20.1.0", "@angular/material": "^20.1.2", "@angular/platform-browser": "^20.1.0", "@angular/platform-server": "^20.1.0", "@angular/router": "^20.1.0", "@angular/ssr": "^20.1.1", "date-fns": "^4.1.0", "express": "^5.1.0", "firebase": "^11.10.0", "lucide-angular": "^0.525.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular/build": "^20.1.1", "@angular/cli": "^20.1.1", "@angular/compiler-cli": "^20.1.0", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "jasmine-core": "~5.8.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2"}}