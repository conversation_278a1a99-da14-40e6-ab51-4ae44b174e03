/**
 * @component @name Origami
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTJWNGExIDEgMCAwIDEgMS0xaDYuMjk3YTEgMSAwIDAgMSAuNjUxIDEuNzU5bC00LjY5NiA0LjAyNSIgLz4KICA8cGF0aCBkPSJtMTIgMjEtNy40MTQtNy40MTRBMiAyIDAgMCAxIDQgMTIuMTcyVjYuNDE1YTEuMDAyIDEuMDAyIDAgMCAxIDEuNzA3LS43MDdMMjAgMjAuMDA5IiAvPgogIDxwYXRoIGQ9Im0xMi4yMTQgMy4zODEgOC40MTQgMTQuOTY2YTEgMSAwIDAgMS0uMTY3IDEuMTk5bC0xLjE2OCAxLjE2M2ExIDEgMCAwIDEtLjcwNi4yOTFINi4zNTFhMSAxIDAgMCAxLS42MjUtLjIxOUwzLjI1IDE4LjhhMSAxIDAgMCAxIC42MzEtMS43ODFsNC4xNjUuMDI3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/origami
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Origami = [
    ['path', { d: 'M12 12V4a1 1 0 0 1 1-1h6.297a1 1 0 0 1 .651 1.759l-4.696 4.025', key: '1bx4vc' }],
    [
        'path',
        {
            d: 'm12 21-7.414-7.414A2 2 0 0 1 4 12.172V6.415a1.002 1.002 0 0 1 1.707-.707L20 20.009',
            key: '1h3km6',
        },
    ],
    [
        'path',
        {
            d: 'm12.214 3.381 8.414 14.966a1 1 0 0 1-.167 1.199l-1.168 1.163a1 1 0 0 1-.706.291H6.351a1 1 0 0 1-.625-.219L3.25 18.8a1 1 0 0 1 .631-1.781l4.165.027',
            key: '1hj4wg',
        },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default Origami;
//# sourceMappingURL=data:application/json;base64,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