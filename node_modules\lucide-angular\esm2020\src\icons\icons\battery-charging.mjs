/**
 * @component @name BatteryCharging
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTEgNy0zIDVoNGwtMyA1IiAvPgogIDxwYXRoIGQ9Ik0xNC44NTYgNkgxNmEyIDIgMCAwIDEgMiAydjhhMiAyIDAgMCAxLTIgMmgtMi45MzUiIC8+CiAgPHBhdGggZD0iTTIyIDE0di00IiAvPgogIDxwYXRoIGQ9Ik01LjE0IDE4SDRhMiAyIDAgMCAxLTItMlY4YTIgMiAwIDAgMSAyLTJoMi45MzYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/battery-charging
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BatteryCharging = [
    ['path', { d: 'm11 7-3 5h4l-3 5', key: 'b4a64w' }],
    ['path', { d: 'M14.856 6H16a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.935', key: 'lre1cr' }],
    ['path', { d: 'M22 14v-4', key: '14q9d5' }],
    ['path', { d: 'M5.14 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2.936', key: '13q5k0' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BatteryCharging;
//# sourceMappingURL=data:application/json;base64,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