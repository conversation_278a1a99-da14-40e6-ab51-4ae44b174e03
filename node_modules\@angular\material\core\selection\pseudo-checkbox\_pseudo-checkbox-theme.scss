@use '../../theming/inspection';
@use '../../tokens/token-utils';
@use './m2-pseudo-checkbox';
@use './m3-pseudo-checkbox';
@use 'sass:map';

@mixin _palette-styles($theme, $palette-name) {
  $tokens: map.get(m2-pseudo-checkbox.get-tokens($theme), color);
  @include token-utils.values($tokens);
}

@mixin base($theme) {
  $tokens: map.get(m2-pseudo-checkbox.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-pseudo-checkbox.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs color theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  $tokens: map.get(m2-pseudo-checkbox.get-tokens($theme), color);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-pseudo-checkbox.get-tokens($theme, $color-variant), color);
  }

  @include token-utils.values($tokens);

  @if inspection.get-theme-version($theme) != 1 {
    // Default to the accent color. Note that the pseudo checkboxes are meant to inherit the
    // theme from their parent, rather than implementing their own theming, which is why we
    // don't attach to the `mat-*` classes. Also note that this needs to be below `.mat-primary`
    // in order to allow for the color to be overwritten if the checkbox is inside a parent that
    // has `mat-accent` and is placed inside another parent that has `mat-primary`.
    .mat-primary {
      $tokens: m2-pseudo-checkbox.private-get-color-palette-color-tokens($theme, primary);
      @include token-utils.values($tokens);
    }

    .mat-accent {
      $tokens: m2-pseudo-checkbox.private-get-color-palette-color-tokens($theme, secondary);
      @include token-utils.values($tokens);
    }

    .mat-warn {
      $tokens: m2-pseudo-checkbox.private-get-color-palette-color-tokens($theme, error);
      @include token-utils.values($tokens);
    }
  }
}

@mixin typography($theme) {
  $tokens: map.get(m2-pseudo-checkbox.get-tokens($theme), typography);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-pseudo-checkbox.get-tokens($theme), typography);
  }

  @include token-utils.values($tokens);
}

@mixin density($theme) {
  $tokens: map.get(m2-pseudo-checkbox.get-tokens($theme), density);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-pseudo-checkbox.get-tokens($theme), density);
  }

  @include token-utils.values($tokens);
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: pseudo-checkbox,
      tokens: token-utils.get-overrides(m3-pseudo-checkbox.get-tokens(), pseudo-checkbox)
    ),
  );
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-pseudo-checkbox.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme, $color-variant);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
