/**
 * @component @name BetweenVerticalStart
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeD0iMyIgeT0iOCIgcng9IjEiIC8+CiAgPHBhdGggZD0ibTE1IDItMyAzLTMtMyIgLz4KICA8cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIxMyIgeD0iMTQiIHk9IjgiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/between-vertical-start
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BetweenVerticalStart = [
    ['rect', { width: '7', height: '13', x: '3', y: '8', rx: '1', key: '1fjrkv' }],
    ['path', { d: 'm15 2-3 3-3-3', key: '1uh6eb' }],
    ['rect', { width: '7', height: '13', x: '14', y: '8', rx: '1', key: 'w3fjg8' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BetweenVerticalStart;
//# sourceMappingURL=data:application/json;base64,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