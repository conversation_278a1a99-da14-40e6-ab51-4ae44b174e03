/**
 * @component @name BicepsFlexed
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIuNDA5IDEzLjAxN0E1IDUgMCAwIDEgMjIgMTVjMCAzLjg2Ni00IDctOSA3LTQuMDc3IDAtOC4xNTMtLjgyLTEwLjM3MS0yLjQ2Mi0uNDI2LS4zMTYtLjYzMS0uODMyLS42Mi0xLjM2MkMyLjExOCAxMi43MjMgMi42MjcgMiAxMCAyYTMgMyAwIDAgMSAzIDMgMiAyIDAgMCAxLTIgMmMtMS4xMDUgMC0xLjY0LS40NDQtMi0xIiAvPgogIDxwYXRoIGQ9Ik0xNSAxNGE1IDUgMCAwIDAtNy41ODQgMiIgLz4KICA8cGF0aCBkPSJNOS45NjQgNi44MjVDOC4wMTkgNy45NzcgOS41IDEzIDggMTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/biceps-flexed
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BicepsFlexed = [
    [
        'path',
        {
            d: 'M12.409 13.017A5 5 0 0 1 22 15c0 3.866-4 7-9 7-4.077 0-8.153-.82-10.371-2.462-.426-.316-.631-.832-.62-1.362C2.118 12.723 2.627 2 10 2a3 3 0 0 1 3 3 2 2 0 0 1-2 2c-1.105 0-1.64-.444-2-1',
            key: '1pmlyh',
        },
    ],
    ['path', { d: 'M15 14a5 5 0 0 0-7.584 2', key: '5rb254' }],
    ['path', { d: 'M9.964 6.825C8.019 7.977 9.5 13 8 15', key: 'kbvsx9' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BicepsFlexed;
//# sourceMappingURL=data:application/json;base64,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