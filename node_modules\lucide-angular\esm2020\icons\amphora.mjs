/**
 * @component @name Amphora
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnY1LjYzMmMwIC40MjQtLjI3Mi43OTUtLjY1My45ODJBNiA2IDAgMCAwIDYgMTRjLjAwNiA0IDMgNyA1IDgiIC8+CiAgPHBhdGggZD0iTTEwIDVIOGEyIDIgMCAwIDAgMCA0aC42OCIgLz4KICA8cGF0aCBkPSJNMTQgMnY1LjYzMmMwIC40MjQuMjcyLjc5NS42NTIuOTgyQTYgNiAwIDAgMSAxOCAxNGMwIDQtMyA3LTUgOCIgLz4KICA8cGF0aCBkPSJNMTQgNWgyYTIgMiAwIDAgMSAwIDRoLS42OCIgLz4KICA8cGF0aCBkPSJNMTggMjJINiIgLz4KICA8cGF0aCBkPSJNOSAyaDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/amphora
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Amphora = [
    [
        'path',
        { d: 'M10 2v5.632c0 .424-.272.795-.653.982A6 6 0 0 0 6 14c.006 4 3 7 5 8', key: '1h8rid' },
    ],
    ['path', { d: 'M10 5H8a2 2 0 0 0 0 4h.68', key: '3ezsi6' }],
    ['path', { d: 'M14 2v5.632c0 .424.272.795.652.982A6 6 0 0 1 18 14c0 4-3 7-5 8', key: 'yt6q09' }],
    ['path', { d: 'M14 5h2a2 2 0 0 1 0 4h-.68', key: '8f95yk' }],
    ['path', { d: 'M18 22H6', key: 'mg6kv4' }],
    ['path', { d: 'M9 2h6', key: '1jrp98' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Amphora;
//# sourceMappingURL=data:application/json;base64,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