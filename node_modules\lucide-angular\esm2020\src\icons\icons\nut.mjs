/**
 * @component @name Nut
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNFYyIiAvPgogIDxwYXRoIGQ9Ik01IDEwdjRhNy4wMDQgNy4wMDQgMCAwIDAgNS4yNzcgNi43ODdjLjQxMi4xMDQuODAyLjI5MiAxLjEwMi41OTJMMTIgMjJsLjYyMS0uNjIxYy4zLS4zLjY5LS40ODggMS4xMDItLjU5MkE3LjAwMyA3LjAwMyAwIDAgMCAxOSAxNHYtNCIgLz4KICA8cGF0aCBkPSJNMTIgNEM4IDQgNC41IDYgNCA4Yy0uMjQzLjk3LS45MTkgMS45NTItMiAzIDEuMzEtLjA4MiAxLjk3Mi0uMjkgMy0xIC41NC45Mi45ODIgMS4zNTYgMiAyIDEuNDUyLS42NDcgMS45NTQtMS4wOTggMi41LTIgLjU5NS45OTUgMS4xNTEgMS40MjcgMi41IDIgMS4zMS0uNjIxIDEuODYyLTEuMDU4IDIuNS0yIC42MjkuOTc3IDEuMTYyIDEuNDIzIDIuNSAyIDEuMjA5LS41NDggMS42OC0uOTY3IDItMiAxLjAzMi45MTYgMS42ODMgMS4xNTcgMyAxLTEuMjk3LTEuMDM2LTEuNzU4LTIuMDMtMi0zLS41LTItNC00LTgtNFoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/nut
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Nut = [
    ['path', { d: 'M12 4V2', key: '1k5q1u' }],
    [
        'path',
        {
            d: 'M5 10v4a7.004 7.004 0 0 0 5.277 6.787c.412.104.802.292 1.102.592L12 22l.621-.621c.3-.3.69-.488 1.102-.592A7.003 7.003 0 0 0 19 14v-4',
            key: '1tgyif',
        },
    ],
    [
        'path',
        {
            d: 'M12 4C8 4 4.5 6 4 8c-.243.97-.919 1.952-2 3 1.31-.082 1.972-.29 3-1 .54.92.982 1.356 2 2 1.452-.647 1.954-1.098 2.5-2 .595.995 1.151 1.427 2.5 2 1.31-.621 1.862-1.058 2.5-2 .629.977 1.162 1.423 2.5 2 1.209-.548 1.68-.967 2-2 1.032.916 1.683 1.157 3 1-1.297-1.036-1.758-2.03-2-3-.5-2-4-4-8-4Z',
            key: 'tnsqj',
        },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default Nut;
//# sourceMappingURL=data:application/json;base64,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