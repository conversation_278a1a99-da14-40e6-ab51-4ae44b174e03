/**
 * @component @name RouteOff
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSI2IiBjeT0iMTkiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTkgMTloOC41Yy40IDAgLjktLjEgMS4zLS4yIiAvPgogIDxwYXRoIGQ9Ik01LjIgNS4yQTMuNSAzLjUzIDAgMCAwIDYuNSAxMkgxMiIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgogIDxwYXRoIGQ9Ik0yMSAxNS4zYTMuNSAzLjUgMCAwIDAtMy4zLTMuMyIgLz4KICA8cGF0aCBkPSJNMTUgNWgtNC4zIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iNSIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/route-off
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const RouteOff = [
    ['circle', { cx: '6', cy: '19', r: '3', key: '1kj8tv' }],
    ['path', { d: 'M9 19h8.5c.4 0 .9-.1 1.3-.2', key: '1effex' }],
    ['path', { d: 'M5.2 5.2A3.5 3.53 0 0 0 6.5 12H12', key: 'k9y2ds' }],
    ['path', { d: 'm2 2 20 20', key: '1ooewy' }],
    ['path', { d: 'M21 15.3a3.5 3.5 0 0 0-3.3-3.3', key: '11nlu2' }],
    ['path', { d: 'M15 5h-4.3', key: '6537je' }],
    ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],
]; //eslint-disable-line no-shadow-restricted-names
export default RouteOff;
//# sourceMappingURL=data:application/json;base64,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