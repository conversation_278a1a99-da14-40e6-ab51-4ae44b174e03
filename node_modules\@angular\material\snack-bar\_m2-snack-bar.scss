@use '../core/tokens/m2-utils';
@use 'sass:map';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (
      snack-bar-container-shape: 4px,
    ),
    color: (
      snack-bar-container-color: map.get($system, inverse-surface),
      snack-bar-supporting-text-color: map.get($system, inverse-on-surface),
      snack-bar-button-color: map.get($system, inverse-primary)
    ),
    typography: (
      snack-bar-supporting-text-font: map.get($system, body-medium-font),
      snack-bar-supporting-text-line-height: map.get($system, body-medium-line-height),
      snack-bar-supporting-text-size: map.get($system, body-medium-size),
      snack-bar-supporting-text-weight: map.get($system, body-medium-weight),
    ),
    density: (),
  );
}
