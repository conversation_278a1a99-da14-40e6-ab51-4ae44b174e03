/**
 * @component @name Beer
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTcgMTFoMWEzIDMgMCAwIDEgMCA2aC0xIiAvPgogIDxwYXRoIGQ9Ik05IDEydjYiIC8+CiAgPHBhdGggZD0iTTEzIDEydjYiIC8+CiAgPHBhdGggZD0iTTE0IDcuNWMtMSAwLTEuNDQuNS0zIC41cy0yLS41LTMtLjUtMS43Mi41LTIuNS41YTIuNSAyLjUgMCAwIDEgMC01Yy43OCAwIDEuNTcuNSAyLjUuNVM5LjQ0IDIgMTEgMnMyIDEuNSAzIDEuNSAxLjcyLS41IDIuNS0uNWEyLjUgMi41IDAgMCAxIDAgNWMtLjc4IDAtMS41LS41LTIuNS0uNVoiIC8+CiAgPHBhdGggZD0iTTUgOHYxMmEyIDIgMCAwIDAgMiAyaDhhMiAyIDAgMCAwIDItMlY4IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/beer
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Beer = [
    ['path', { d: 'M17 11h1a3 3 0 0 1 0 6h-1', key: '1yp76v' }],
    ['path', { d: 'M9 12v6', key: '1u1cab' }],
    ['path', { d: 'M13 12v6', key: '1sugkk' }],
    [
        'path',
        {
            d: 'M14 7.5c-1 0-1.44.5-3 .5s-2-.5-3-.5-1.72.5-2.5.5a2.5 2.5 0 0 1 0-5c.78 0 1.57.5 2.5.5S9.44 2 11 2s2 1.5 3 1.5 1.72-.5 2.5-.5a2.5 2.5 0 0 1 0 5c-.78 0-1.5-.5-2.5-.5Z',
            key: '1510fo',
        },
    ],
    ['path', { d: 'M5 8v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V8', key: '19jb7n' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Beer;
//# sourceMappingURL=data:application/json;base64,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