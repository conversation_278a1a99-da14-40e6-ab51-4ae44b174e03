/**
 * @component @name Anvil
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAxMEg2YTQgNCAwIDAgMS00LTQgMSAxIDAgMCAxIDEtMWg0IiAvPgogIDxwYXRoIGQ9Ik03IDVhMSAxIDAgMCAxIDEtMWgxM2ExIDEgMCAwIDEgMSAxIDcgNyAwIDAgMS03IDdIOGExIDEgMCAwIDEtMS0xeiIgLz4KICA8cGF0aCBkPSJNOSAxMnY1IiAvPgogIDxwYXRoIGQ9Ik0xNSAxMnY1IiAvPgogIDxwYXRoIGQ9Ik01IDIwYTMgMyAwIDAgMSAzLTNoOGEzIDMgMCAwIDEgMyAzIDEgMSAwIDAgMS0xIDFINmExIDEgMCAwIDEtMS0xIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/anvil
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Anvil = [
    ['path', { d: 'M7 10H6a4 4 0 0 1-4-4 1 1 0 0 1 1-1h4', key: '1hjpb6' }],
    [
        'path',
        { d: 'M7 5a1 1 0 0 1 1-1h13a1 1 0 0 1 1 1 7 7 0 0 1-7 7H8a1 1 0 0 1-1-1z', key: '1qn45f' },
    ],
    ['path', { d: 'M9 12v5', key: '3anwtq' }],
    ['path', { d: 'M15 12v5', key: '5xh3zn' }],
    [
        'path',
        { d: 'M5 20a3 3 0 0 1 3-3h8a3 3 0 0 1 3 3 1 1 0 0 1-1 1H6a1 1 0 0 1-1-1', key: '1fi4x8' },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default Anvil;
//# sourceMappingURL=data:application/json;base64,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