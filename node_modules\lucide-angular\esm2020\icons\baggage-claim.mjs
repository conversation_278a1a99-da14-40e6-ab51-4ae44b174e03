/**
 * @component @name BaggageClaim
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMThINmEyIDIgMCAwIDEtMi0yVjdhMiAyIDAgMCAwLTItMiIgLz4KICA8cGF0aCBkPSJNMTcgMTRWNGEyIDIgMCAwIDAtMi0yaC0xYTIgMiAwIDAgMC0yIDJ2MTAiIC8+CiAgPHJlY3Qgd2lkdGg9IjEzIiBoZWlnaHQ9IjgiIHg9IjgiIHk9IjYiIHJ4PSIxIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMjAiIHI9IjIiIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjIwIiByPSIyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/baggage-claim
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BaggageClaim = [
    ['path', { d: 'M22 18H6a2 2 0 0 1-2-2V7a2 2 0 0 0-2-2', key: '4irg2o' }],
    ['path', { d: 'M17 14V4a2 2 0 0 0-2-2h-1a2 2 0 0 0-2 2v10', key: '14fcyx' }],
    ['rect', { width: '13', height: '8', x: '8', y: '6', rx: '1', key: 'o6oiis' }],
    ['circle', { cx: '18', cy: '20', r: '2', key: 't9985n' }],
    ['circle', { cx: '9', cy: '20', r: '2', key: 'e5v82j' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BaggageClaim;
//# sourceMappingURL=data:application/json;base64,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