/**
 * @component @name PaintbrushVertical
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMnYyIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjQiIC8+CiAgPHBhdGggZD0iTTE3IDJhMSAxIDAgMCAxIDEgMXY5SDZWM2ExIDEgMCAwIDEgMS0xeiIgLz4KICA8cGF0aCBkPSJNNiAxMmExIDEgMCAwIDAtMSAxdjFhMiAyIDAgMCAwIDIgMmgyYTEgMSAwIDAgMSAxIDF2Mi45YTIgMiAwIDEgMCA0IDBWMTdhMSAxIDAgMCAxIDEtMWgyYTIgMiAwIDAgMCAyLTJ2LTFhMSAxIDAgMCAwLTEtMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/paintbrush-vertical
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const PaintbrushVertical = [
    ['path', { d: 'M10 2v2', key: '7u0qdc' }],
    ['path', { d: 'M14 2v4', key: 'qmzblu' }],
    ['path', { d: 'M17 2a1 1 0 0 1 1 1v9H6V3a1 1 0 0 1 1-1z', key: 'ycvu00' }],
    [
        'path',
        {
            d: 'M6 12a1 1 0 0 0-1 1v1a2 2 0 0 0 2 2h2a1 1 0 0 1 1 1v2.9a2 2 0 1 0 4 0V17a1 1 0 0 1 1-1h2a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1',
            key: 'iw4wnp',
        },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default PaintbrushVertical;
//# sourceMappingURL=data:application/json;base64,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