/**
 * @component @name PackageOpen
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMjJ2LTkiIC8+CiAgPHBhdGggZD0iTTE1LjE3IDIuMjFhMS42NyAxLjY3IDAgMCAxIDEuNjMgMEwyMSA0LjU3YTEuOTMgMS45MyAwIDAgMSAwIDMuMzZMOC44MiAxNC43OWExLjY1NSAxLjY1NSAwIDAgMS0xLjY0IDBMMyAxMi40M2ExLjkzIDEuOTMgMCAwIDEgMC0zLjM2eiIgLz4KICA8cGF0aCBkPSJNMjAgMTN2My44N2EyLjA2IDIuMDYgMCAwIDEtMS4xMSAxLjgzbC02IDMuMDhhMS45MyAxLjkzIDAgMCAxLTEuNzggMGwtNi0zLjA4QTIuMDYgMi4wNiAwIDAgMSA0IDE2Ljg3VjEzIiAvPgogIDxwYXRoIGQ9Ik0yMSAxMi40M2ExLjkzIDEuOTMgMCAwIDAgMC0zLjM2TDguODMgMi4yYTEuNjQgMS42NCAwIDAgMC0xLjYzIDBMMyA0LjU3YTEuOTMgMS45MyAwIDAgMCAwIDMuMzZsMTIuMTggNi44NmExLjYzNiAxLjYzNiAwIDAgMCAxLjYzIDB6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/package-open
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const PackageOpen = [
    ['path', { d: 'M12 22v-9', key: 'x3hkom' }],
    [
        'path',
        {
            d: 'M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z',
            key: '2ntwy6',
        },
    ],
    [
        'path',
        {
            d: 'M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13',
            key: '1pmm1c',
        },
    ],
    [
        'path',
        {
            d: 'M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z',
            key: '12ttoo',
        },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default PackageOpen;
//# sourceMappingURL=data:application/json;base64,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