/**
 * @component @name Banana
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxM2MzLjUtMiA4LTIgMTAgMmE1LjUgNS41IDAgMCAxIDggNSIgLz4KICA8cGF0aCBkPSJNNS4xNSAxNy44OWM1LjUyLTEuNTIgOC42NS02Ljg5IDctMTJDMTEuNTUgNCAxMS41IDIgMTMgMmMzLjIyIDAgNSA1LjUgNSA4IDAgNi41LTQuMiAxMi0xMC40OSAxMkM1LjExIDIyIDIgMjIgMiAyMGMwLTEuNSAxLjE0LTEuNTUgMy4xNS0yLjExWiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/banana
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Banana = [
    ['path', { d: 'M4 13c3.5-2 8-2 10 2a5.5 5.5 0 0 1 8 5', key: '1cscit' }],
    [
        'path',
        {
            d: 'M5.15 17.89c5.52-1.52 8.65-6.89 7-12C11.55 4 11.5 2 13 2c3.22 0 5 5.5 5 8 0 6.5-4.2 12-10.49 12C5.11 22 2 22 2 20c0-1.5 1.14-1.55 3.15-2.11Z',
            key: '1y1nbv',
        },
    ],
]; //eslint-disable-line no-shadow-restricted-names
export default Banana;
//# sourceMappingURL=data:application/json;base64,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