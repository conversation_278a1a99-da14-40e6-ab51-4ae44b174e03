/**
 * @component @name PackageX
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTBWOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczbDcgNGEyIDIgMCAwIDAgMiAwbDItMS4xNCIgLz4KICA8cGF0aCBkPSJtNy41IDQuMjcgOSA1LjE1IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjMuMjkgNyAxMiAxMiAyMC43MSA3IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMjIiIHkyPSIxMiIgLz4KICA8cGF0aCBkPSJtMTcgMTMgNSA1bS01IDAgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/package-x
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const PackageX = [
    [
        'path',
        {
            d: 'M21 10V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l2-1.14',
            key: 'e7tb2h',
        },
    ],
    ['path', { d: 'm7.5 4.27 9 5.15', key: '1c824w' }],
    ['polyline', { points: '3.29 7 12 12 20.71 7', key: 'ousv84' }],
    ['line', { x1: '12', x2: '12', y1: '22', y2: '12', key: 'a4e8g8' }],
    ['path', { d: 'm17 13 5 5m-5 0 5-5', key: 'im3w4b' }],
]; //eslint-disable-line no-shadow-restricted-names
export default PackageX;
//# sourceMappingURL=data:application/json;base64,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