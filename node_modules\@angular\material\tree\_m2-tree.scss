@use 'sass:map';
@use '../core/theming/theming';
@use '../core/tokens/m2-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -4);

  @return (
    base: (),
    color: (
      tree-container-background-color: map.get($system, surface),
      tree-node-text-color: map.get($system, on-surface),
    ),
    typography: (
      // TODO(crisbeto): provide tokens for line height and letter spacing
      tree-node-text-font: map.get($system, body-medium-font),
      tree-node-text-size: map.get($system, body-medium-size),
      tree-node-text-weight: map.get($system, body-medium-weight),),
    density: (
      tree-node-min-height: map.get((
        0: 48px,
        -1: 44px,
        -2: 40px,
        -3: 36px,
        -4: 28px,
      ), $density-scale)
    ),
  );
}
