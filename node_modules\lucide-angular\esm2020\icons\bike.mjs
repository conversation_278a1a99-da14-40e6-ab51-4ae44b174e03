/**
 * @component @name Bike
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOC41IiBjeT0iMTcuNSIgcj0iMy41IiAvPgogIDxjaXJjbGUgY3g9IjUuNSIgY3k9IjE3LjUiIHI9IjMuNSIgLz4KICA8Y2lyY2xlIGN4PSIxNSIgY3k9IjUiIHI9IjEiIC8+CiAgPHBhdGggZD0iTTEyIDE3LjVWMTRsLTMtMyA0LTMgMiAzaDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bike
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Bike = [
    ['circle', { cx: '18.5', cy: '17.5', r: '3.5', key: '15x4ox' }],
    ['circle', { cx: '5.5', cy: '17.5', r: '3.5', key: '1noe27' }],
    ['circle', { cx: '15', cy: '5', r: '1', key: '19l28e' }],
    ['path', { d: 'M12 17.5V14l-3-3 4-3 2 3h2', key: '1npguv' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Bike;
//# sourceMappingURL=data:application/json;base64,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