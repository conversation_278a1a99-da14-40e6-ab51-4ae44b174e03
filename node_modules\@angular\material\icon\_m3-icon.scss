@use 'sass:map';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-icon.
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  $color: inherit;
  @if $color-variant {
    $color: map.get($system, $color-variant);
  }

  @return (
    base: (),
    color: (
      icon-color: $color,
    ),
    typography: (),
    density: (),
  );
}
