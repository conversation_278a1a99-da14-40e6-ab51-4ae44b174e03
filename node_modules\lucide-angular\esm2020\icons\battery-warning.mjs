/**
 * @component @name BatteryWarning
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTdoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCA3djYiIC8+CiAgPHBhdGggZD0iTTE0IDZoMmEyIDIgMCAwIDEgMiAydjhhMiAyIDAgMCAxLTIgMmgtMiIgLz4KICA8cGF0aCBkPSJNMjIgMTR2LTQiIC8+CiAgPHBhdGggZD0iTTYgMThINGEyIDIgMCAwIDEtMi0yVjhhMiAyIDAgMCAxIDItMmgyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/battery-warning
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const BatteryWarning = [
    ['path', { d: 'M10 17h.01', key: 'nbq80n' }],
    ['path', { d: 'M10 7v6', key: 'nne03l' }],
    ['path', { d: 'M14 6h2a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2', key: '1m83kb' }],
    ['path', { d: 'M22 14v-4', key: '14q9d5' }],
    ['path', { d: 'M6 18H4a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h2', key: 'h8lgfh' }],
]; //eslint-disable-line no-shadow-restricted-names
export default BatteryWarning;
//# sourceMappingURL=data:application/json;base64,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