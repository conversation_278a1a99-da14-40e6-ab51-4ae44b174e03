/**
 * @component @name Alarm<PERSON>lock<PERSON>heck
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEzIiByPSI4IiAvPgogIDxwYXRoIGQ9Ik01IDMgMiA2IiAvPgogIDxwYXRoIGQ9Im0yMiA2LTMtMyIgLz4KICA8cGF0aCBkPSJNNi4zOCAxOC43IDQgMjEiIC8+CiAgPHBhdGggZD0iTTE3LjY0IDE4LjY3IDIwIDIxIiAvPgogIDxwYXRoIGQ9Im05IDEzIDIgMiA0LTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/alarm-clock-check
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const AlarmClockCheck = [
    ['circle', { cx: '12', cy: '13', r: '8', key: '3y4lt7' }],
    ['path', { d: 'M5 3 2 6', key: '18tl5t' }],
    ['path', { d: 'm22 6-3-3', key: '1opdir' }],
    ['path', { d: 'M6.38 18.7 4 21', key: '17xu3x' }],
    ['path', { d: 'M17.64 18.67 20 21', key: 'kv2oe2' }],
    ['path', { d: 'm9 13 2 2 4-4', key: '6343dt' }],
]; //eslint-disable-line no-shadow-restricted-names
export default AlarmClockCheck;
//# sourceMappingURL=data:application/json;base64,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