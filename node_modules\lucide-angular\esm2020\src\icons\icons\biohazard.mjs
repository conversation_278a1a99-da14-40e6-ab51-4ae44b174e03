/**
 * @component @name Biohazard
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjExLjkiIHI9IjIiIC8+CiAgPHBhdGggZD0iTTYuNyAzLjRjLS45IDIuNSAwIDUuMiAyLjIgNi43QzYuNSA5IDMuNyA5LjYgMiAxMS42IiAvPgogIDxwYXRoIGQ9Im04LjkgMTAuMSAxLjQuOCIgLz4KICA8cGF0aCBkPSJNMTcuMyAzLjRjLjkgMi41IDAgNS4yLTIuMiA2LjcgMi40LTEuMiA1LjItLjYgNi45IDEuNSIgLz4KICA8cGF0aCBkPSJtMTUuMSAxMC4xLTEuNC44IiAvPgogIDxwYXRoIGQ9Ik0xNi43IDIwLjhjLTIuNi0uNC00LjYtMi42LTQuNy01LjMtLjIgMi42LTIuMSA0LjgtNC43IDUuMiIgLz4KICA8cGF0aCBkPSJNMTIgMTMuOXYxLjYiIC8+CiAgPHBhdGggZD0iTTEzLjUgNS40Yy0xLS4yLTItLjItMyAwIiAvPgogIDxwYXRoIGQ9Ik0xNyAxNi40Yy43LS43IDEuMi0xLjYgMS41LTIuNSIgLz4KICA8cGF0aCBkPSJNNS41IDEzLjljLjMuOS44IDEuOCAxLjUgMi41IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/biohazard
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Biohazard = [
    ['circle', { cx: '12', cy: '11.9', r: '2', key: 'e8h31w' }],
    ['path', { d: 'M6.7 3.4c-.9 2.5 0 5.2 2.2 6.7C6.5 9 3.7 9.6 2 11.6', key: '17bolr' }],
    ['path', { d: 'm8.9 10.1 1.4.8', key: '15ezny' }],
    ['path', { d: 'M17.3 3.4c.9 2.5 0 5.2-2.2 6.7 2.4-1.2 5.2-.6 6.9 1.5', key: 'wtwa5u' }],
    ['path', { d: 'm15.1 10.1-1.4.8', key: '1r0b28' }],
    ['path', { d: 'M16.7 20.8c-2.6-.4-4.6-2.6-4.7-5.3-.2 2.6-2.1 4.8-4.7 5.2', key: 'm7qszh' }],
    ['path', { d: 'M12 13.9v1.6', key: 'zfyyim' }],
    ['path', { d: 'M13.5 5.4c-1-.2-2-.2-3 0', key: '1bi9q0' }],
    ['path', { d: 'M17 16.4c.7-.7 1.2-1.6 1.5-2.5', key: '1rhjqw' }],
    ['path', { d: 'M5.5 13.9c.3.9.8 1.8 1.5 2.5', key: '8gsud3' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Biohazard;
//# sourceMappingURL=data:application/json;base64,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