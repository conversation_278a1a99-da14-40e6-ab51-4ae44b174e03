@use 'sass:map';
@use '../core/style/elevation';
@use '../core/tokens/m3-utils';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-autocomplete.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-autocomplete
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);
  $tokens: (
    base: (
      autocomplete-container-shape: map.get($system, corner-extra-small),
      autocomplete-container-elevation-shadow: elevation.get-box-shadow(2),
    ),
    color: (
      autocomplete-background-color: map.get($system, surface-container),
    ),
    typography: (),
    density: (),
  );

  @return $tokens;
}
