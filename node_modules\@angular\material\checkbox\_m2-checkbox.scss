@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -3);

  @return (
    base: (
      checkbox-disabled-selected-checkmark-color: map.get($system, surface),
      checkbox-selected-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      checkbox-selected-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      checkbox-selected-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      checkbox-unselected-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      checkbox-unselected-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      checkbox-unselected-pressed-state-layer-opacity:
          map.get($system, pressed-state-layer-opacity),
    ),
    color: private-get-color-palette-color-tokens($theme, secondary),
    typography: (
      checkbox-label-text-font: map.get($system, body-medium-font),
      checkbox-label-text-line-height: map.get($system, body-medium-line-height),
      checkbox-label-text-size: map.get($system, body-medium-size),
      checkbox-label-text-tracking: map.get($system, body-medium-tracking),
      checkbox-label-text-weight: map.get($system, body-medium-weight)
    ),
    density: (
      checkbox-touch-target-display: if($density-scale < -1, none, block),
      checkbox-state-layer-size: map.get((
        0: 40px,
        -1: 36px,
        -2: 32px,
        -3: 28px,
      ), $density-scale)
    ),
  );
}

@function private-get-color-palette-color-tokens($theme, $color-variant, $exclude: ()) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, secondary, $color-variant);
  $disabled: m3-utils.color-with-opacity(map.get($system, on-surface), 38%);

  $tokens: (
    checkbox-disabled-label-color: $disabled,
    checkbox-label-text-color: map.get($system, on-surface),
    checkbox-disabled-selected-icon-color: $disabled,
    checkbox-disabled-unselected-icon-color: $disabled,
    checkbox-selected-checkmark-color: map.get($system, on-secondary),
    checkbox-selected-focus-icon-color: map.get($system, secondary),
    checkbox-selected-hover-icon-color: map.get($system, secondary),
    checkbox-selected-icon-color: map.get($system, secondary),
    checkbox-selected-pressed-icon-color: map.get($system, secondary),
    checkbox-unselected-focus-icon-color: map.get($system, on-surface),
    checkbox-unselected-hover-icon-color: map.get($system, on-surface),
    checkbox-unselected-icon-color: map.get($system, on-surface-variant),
    checkbox-selected-focus-state-layer-color: map.get($system, secondary),
    checkbox-selected-hover-state-layer-color: map.get($system, secondary),
    checkbox-selected-pressed-state-layer-color: map.get($system, secondary),
    checkbox-unselected-focus-state-layer-color: map.get($system, on-surface),
    checkbox-unselected-hover-state-layer-color: map.get($system, on-surface),
    checkbox-unselected-pressed-state-layer-color: map.get($system, on-surface),
  );

  @each $token in $exclude {
    $tokens: map.remove($tokens, $token);
  }

  @return $tokens;
}
