/**
 * @component @name Bird
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2guMDEiIC8+CiAgPHBhdGggZD0iTTMuNCAxOEgxMmE4IDggMCAwIDAgOC04VjdhNCA0IDAgMCAwLTcuMjgtMi4zTDIgMjAiIC8+CiAgPHBhdGggZD0ibTIwIDcgMiAuNS0yIC41IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOHYzIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNy43NVYyMSIgLz4KICA8cGF0aCBkPSJNNyAxOGE2IDYgMCAwIDAgMy44NC0xMC42MSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bird
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Bird = [
    ['path', { d: 'M16 7h.01', key: '1kdx03' }],
    ['path', { d: 'M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20', key: 'oj1oa8' }],
    ['path', { d: 'm20 7 2 .5-2 .5', key: '12nv4d' }],
    ['path', { d: 'M10 18v3', key: '1yea0a' }],
    ['path', { d: 'M14 17.75V21', key: '1pymcb' }],
    ['path', { d: 'M7 18a6 6 0 0 0 3.84-10.61', key: '1npnn0' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Bird;
//# sourceMappingURL=data:application/json;base64,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