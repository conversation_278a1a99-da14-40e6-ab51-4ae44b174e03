/**
 * @component @name ThermometerSun
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgOWE0IDQgMCAwIDAtMiA3LjUiIC8+CiAgPHBhdGggZD0iTTEyIDN2MiIgLz4KICA8cGF0aCBkPSJtNi42IDE4LjQtMS40IDEuNCIgLz4KICA8cGF0aCBkPSJNMjAgNHYxMC41NGE0IDQgMCAxIDEtNCAwVjRhMiAyIDAgMCAxIDQgMFoiIC8+CiAgPHBhdGggZD0iTTQgMTNIMiIgLz4KICA8cGF0aCBkPSJNNi4zNCA3LjM0IDQuOTMgNS45MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/thermometer-sun
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const ThermometerSun = [
    ['path', { d: 'M12 9a4 4 0 0 0-2 7.5', key: '1jvsq6' }],
    ['path', { d: 'M12 3v2', key: '1w22ol' }],
    ['path', { d: 'm6.6 18.4-1.4 1.4', key: 'w2yidj' }],
    ['path', { d: 'M20 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z', key: 'iof6y5' }],
    ['path', { d: 'M4 13H2', key: '118le4' }],
    ['path', { d: 'M6.34 7.34 4.93 5.93', key: '1brd51' }],
]; //eslint-disable-line no-shadow-restricted-names
export default ThermometerSun;
//# sourceMappingURL=data:application/json;base64,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