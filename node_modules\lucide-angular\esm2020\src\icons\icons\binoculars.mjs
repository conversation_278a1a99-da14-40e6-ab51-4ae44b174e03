/**
 * @component @name Binoculars
 * @description Lucide SVG icon component, renders SVG Element with children.
 *
 * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTBoNCIgLz4KICA8cGF0aCBkPSJNMTkgN1Y0YTEgMSAwIDAgMC0xLTFoLTJhMSAxIDAgMCAwLTEgMXYzIiAvPgogIDxwYXRoIGQ9Ik0yMCAyMWEyIDIgMCAwIDAgMi0ydi0zLjg1MWMwLTEuMzktMi0yLjk2Mi0yLTQuODI5VjhhMSAxIDAgMCAwLTEtMWgtNGExIDEgMCAwIDAtMSAxdjExYTIgMiAwIDAgMCAyIDJ6IiAvPgogIDxwYXRoIGQ9Ik0gMjIgMTYgTCAyIDE2IiAvPgogIDxwYXRoIGQ9Ik00IDIxYTIgMiAwIDAgMS0yLTJ2LTMuODUxYzAtMS4zOSAyLTIuOTYyIDItNC44MjlWOGExIDEgMCAwIDEgMS0xaDRhMSAxIDAgMCAxIDEgMXYxMWEyIDIgMCAwIDEtMiAyeiIgLz4KICA8cGF0aCBkPSJNOSA3VjRhMSAxIDAgMCAwLTEtMUg2YTEgMSAwIDAgMC0xIDF2MyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/binoculars
 * @see https://lucide.dev/guide/packages/lucide-vue-next - Documentation
 *
 * @param {Object} props - Lucide icons props and any valid SVG attribute
 * @returns {FunctionalComponent} Vue component
 *
 */
const Binoculars = [
    ['path', { d: 'M10 10h4', key: 'tcdvrf' }],
    ['path', { d: 'M19 7V4a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v3', key: '3apit1' }],
    [
        'path',
        {
            d: 'M20 21a2 2 0 0 0 2-2v-3.851c0-1.39-2-2.962-2-4.829V8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v11a2 2 0 0 0 2 2z',
            key: 'rhpgnw',
        },
    ],
    ['path', { d: 'M 22 16 L 2 16', key: '14lkq7' }],
    [
        'path',
        {
            d: 'M4 21a2 2 0 0 1-2-2v-3.851c0-1.39 2-2.962 2-4.829V8a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v11a2 2 0 0 1-2 2z',
            key: '104b3k',
        },
    ],
    ['path', { d: 'M9 7V4a1 1 0 0 0-1-1H6a1 1 0 0 0-1 1v3', key: '14fczp' }],
]; //eslint-disable-line no-shadow-restricted-names
export default Binoculars;
//# sourceMappingURL=data:application/json;base64,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